<!DOCTYPE html>
<html lang="zh-tw">
	<head>
		{{template "head" .}}
		{{template "sweet_alert" .}}
		{{template "validate" .}}
		{{template "moment" .}}
		{{template "custom_scrollbar" .}}

		{{template "member.assets" .}}
		{{template "member.index.assets" .}}
		<script src="/common/js/counsel.min.js?v=1009"></script>
	</head>
	<body>
		{{template "top" .}}

		{{template "member/index" .}}

		{{template "bottom" .}}
	</body>

	<script>
		createApp({
			delimiters: ["${", "}"],
			mixins: [CommonMixin, LevelMixin, Member, CounselMix],
			mounted() {
				this.getMemberData();
				this.getMemberPoints();
				this.getMemberSocialPoints();
				this.getAppointments();
			},
			data() {
				return {
					mem_name: "",
					mem_level: 0,
					level_expired_at: "",
					mem_points: 0,
					social_points: 0,
					point_expired_at: "",
					year: new Date().getFullYear(),
					month: this.padZero(new Date().getMonth() + 1, 2),
					t_year: new Date().getFullYear(),
					t_month: this.padZero(new Date().getMonth() + 1, 2),
					t_day: new Date().getDate(),
					appointments: [],
					booking: [],
				};
			},
			methods: {
				getMemberData() {
					axiosRequest()
						.get("/api/members")
						.then((res) => {
							const data = res.data.data;
							this.mem_name = data.name;
							this.mem_level = data.level;
							this.level_expired_at = this.formatDate(data.expired_at);
						})
						.catch((err) => {
							console.log(err);
							console.log(err.response.data);
							msgError(err.response.data.msg);
						});
				},
				getMemberPoints() {
					axiosRequest()
						.get("/api/members/points")
						.then((res) => {
							const data = res.data.data;
							this.mem_points = data.points;
							this.point_expired_at = this.formatDate(data.expired_at);
						})
						.catch((err) => {
							console.log(err);
							console.log(err.response.data);
							msgError(err.response.data.msg);
						});
				},
				getMemberSocialPoints() {
					axiosRequest()
						.get("/api/members/credit")
						.then((res) => {
							this.social_points = res.data.points
						})
						.catch(err => {
							console.log(err);
						})
				},
				getAppointments() {
					axiosRequest()
						.get("/api/members/counsels/appointments", {
							params: {
								date_at: `${this.year}-${this.padZero(this.month, 2)}`,
							},
						})
						.then(res => {
							this.booking = {};

							res.data.data.forEach((item) => {
								const date = this.formatDate(item.start_at);
								
								item.end_at = this.momentAdd(item.start_at, item.duration, "minutes");
								if (this.booking[date] == null) {
									this.booking[date] = [];
								}

								this.booking[date].push(item);
							});
						})
						.catch(err => {
							console.log(err);
						})
						.finally(() => {
							this.generateCalendar();
						})
				},
				generateCalendar() {
					const year = this.year;
					const month = this.month;
					const lastDayOfMonth = new Date(year, month, 0).getDate();
					const firstDayOfWeek = new Date(year, month - 1, 1).getDay();
					const lastDayOfWeek = 6 - new Date(year, month, 0).getDay();
					const lastMonthOfLastDay = new Date(year, month - 1, 0).getDate();
					const nextMonthOfFirstDay = new Date(year, month, 1).getDate();

					this.appointments = [];
					let week = [];
					for (
						let i = 1;
						i <= lastDayOfMonth + firstDayOfWeek + lastDayOfWeek;
						i++
					) {
						if (i <= firstDayOfWeek) {
							week.push({
								year: month === 1 ? year - 1 : year,
								month: this.padZero(month === 1 ? 12 : month - 1, 2),
								day: lastMonthOfLastDay - firstDayOfWeek + i,
							});
						} else if (
							i > firstDayOfWeek &&
							i <= lastDayOfMonth + firstDayOfWeek
						) {
							week.push({
								year: year,
								month: this.padZero(month, 2),
								day: i - firstDayOfWeek,
							});
						} else {
							week.push({
								year: month === 12 ? year + 1 : year,
								month: this.padZero(month === 12 ? 1 : month + 1, 2),
								day: i - lastDayOfMonth - firstDayOfWeek,
							});
						}

						if (i % 7 === 0) {
							this.appointments.push(week);
							week = [];
						}
					}

					this.$nextTick(() => {
						initCustomScrollbar()
					})
				},
				today() {
					this.year = this.t_year;
					this.month = this.t_month;
					this.getAppointments();
				},
				lastMonth() {
					this.month = parseInt(this.month) - 1;

					if (this.month === 0) {
						this.year -= 1;
						this.month = 12;
					}

					this.month = this.padZero(this.month, 2);

					this.getAppointments();
				},
				nextMonth() {
					this.month = parseInt(this.month) + 1;

					if (this.month === 13) {
						this.year += 1;
						this.month = 1;
					}

					this.month = this.padZero(this.month, 2);

					this.getAppointments();
				},
				showBooking(book) {
					if (book.booking == "N") {
						let date_at = this.formatDate(book.start_at).replace(/-/g, "/"),
							start_at = this.formatTime(book.start_at),
							end_at = this.formatTime(book.end_at);

						msgConfirm(
							`請確認<font color=red>${date_at} ${start_at}~${end_at}</font>是否預約<font color=red>【${book.title}】</font>?`,
							() => {
								axiosRequest()
									.patch("/api/members/booking", {
										id: book.id,
									})
									.then((res) => {
										msgTopSuccess(res.data.msg);
										this.getAppointments();
									})
									.catch((err) => {
										console.log(err);
										msgError(err.response.data.msg);
									});
							}
						);
					} else {
						msgbox(
							`<a href="https://calendar.google.com/calendar/event?` +
								`action=TEMPLATE&` +
								`dates=${this.formatGoogleDate(book.start_at)}/${this.formatGoogleDate(book.end_at)}&` +
								`text=${book.title}&` +
								`details=${book.note}&` +
								`" class="btn btn-success mb-3" target="_blank">` +
								`<span class="fa-google fa-brands"></span>` +
								`加入Google行事曆` +
								`</a>` +
								`<br>` +
								`<div class="text-start">${book.note}</div>`
						);
					}
				},
				showStatus(book) {
					let status = this.getCounselStatusTxt(book.status);
					
					if (!status)
						return "";

					return `(${status})`;
				},
				dateChinese(date) {
					if (this.isZeroDate(date)) return "";

					return date.replace("-", "年").replace("-", "月") + "日";
				},
			},
			computed: {
				levelExpiredDate() {
					if (this.isZeroDate(this.level_expired_at)) return "";

					return (
						this.level_expired_at.replace("-", "年").replace("-", "月") + "日"
					);
				},
			},
		}).mount("#app");
	</script>
</html>

{{define "member.index.assets"}}
    <link rel="stylesheet" href="/assets/css/member_index.min.css?v=0115">
    <link rel="stylesheet" href="/assets/css/member_index_rwd.min.css?v=0115">
{{end}}

{{define "member/index"}}
<div id="app" class="memb_top">
	<div class="container">
		{{template "txt_title" .}}

		<div class="member_data">
			<p>${ mem_name } <wbr>歡迎回來，接著進化！</p>
			<div class="row row-cols-1 row-cols-md-2">
				<div class="col">
					學員等級：
					<span v-text="getLevelTxt(mem_level)"></span>
				</div>

				<div class="col" v-if="!isZeroDate(level_expired_at) || (!isZeroDate(point_expired_at) && mem_points > 0)">
					<template v-if="!isZeroDate(level_expired_at)">
						級別到期日：
						<span v-html="dateChinese(level_expired_at)"></span>
						<span v-if="isDateExpired(level_expired_at)">
							(已到期)
						</span>
					</template>
				</div>

				<div class="col col-md-6">
					目前剩餘點數：共
					<span>${ addCommas(mem_points) }</span> 點
				</div>

				<div class="col col-md-6" v-if="!isZeroDate(point_expired_at) && mem_points > 0">
					點數到期日：
					<span v-html="dateChinese(point_expired_at)"></span>
					<span v-if="isDateExpired(point_expired_at)">
						(已到期)
					</span>
				</div>

				<div class="col col-md-6" v-if="social_points > 0">
					目前社群積分：共
					<span v-html="addCommas(social_points)"></span> 點
				</div>

				<div class="col col md-6" v-if="social_points > 0">
					社群積分到期日：
					<span v-html="dateChinese(`${new Date().getFullYear()}-12-31`)"></span>
				</div>
			</div>
		</div>

		<div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 management">
			<div class="col me_om">
				<a href="/member/reg">
					<img src="/assets/images/member/icon_modify.svg" />
					<h4>學員資料修改</h4>
					<p>ig、地址有換記得回來修改</p>
				</a>
			</div>
			<div class="col me_om">
				<a href="/member/order">
					<img src="/assets/images/member/icon_order.svg" />
					<h4>點數紀錄</h4>
					<p>課費使用，點數領取</p>
				</a>
			</div>
			<div class="col me_om">
				<a href="/member/product">
					<img src="/assets/images/member/icon_class.svg" />
					<h4>課程紀錄</h4>
					<p>成長留下的痕跡</p>
				</a>
			</div>
			<div class="col me_om">
				<a href="/member/work">
					<img src="/assets/images/member/icon_operation.svg" />
					<h4>作業區</h4>
					<p>記得按時繳交!全力動腦!</p>
				</a>
			</div>
			<div class="col me_om">
				<a href="/member/counsel">
					<img src="/assets/images/member/counsel.svg" />
					<h4>輔導專區</h4>
				</a>
			</div>
			<div class="col me_om">
				<a href="/member/goods">
					<img src="/assets/images/member/card.svg" />
					<h4>道具區</h4>
				</a>
			</div>
			<div class="col me_om">
				<a href="/member/challenge">
					<img src="/assets/images/member/trophy.svg">
					<h4>闖關挑戰</h4>
				</a>
			</div>
			<div class="col me_om">
				<a href="/member/coupon">
					<img src="/assets/images/member/icon_coupon.svg" alt="coupon">
					<h4>折扣券</h4>
				</a>
			</div>
			<div class="col me_om">
				<a href="/member/signout">
					<img src="/assets/images/member/icon_logout.svg" />
					<h4>學員登出</h4>
					<p class="love">記得定期回來找鞭</p>
				</a>
			</div>
		</div>

		<!-- calendar -->
		<div class="d-calendar">
			<header class="calendar-header">
				<div class="header-left">
					<h2>${ year }年${ month }月</h2>
				</div>

				<div class="header-right">
					<button type="button" @click="today" class="btn today">今天</button>
					<button type="button" @click="lastMonth" class="btn">
						<i><</i>
					</button>
					<button type="button" @click="nextMonth" class="btn">
						<i>></i>
					</button>
				</div>
			</header>

			<table>
				<thead>
					<tr>
						<th>週日</th>
						<th>週一</th>
						<th>週二</th>
						<th>週三</th>
						<th>週四</th>
						<th>週五</th>
						<th>週六</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="week in appointments">
						<td v-for="d in week"
							:class="{ 'disabled': d.month !== month,
                                'today': d.year === t_year && d.month === t_month && d.day === t_day }"
						>
							<div class="item cScroll">
								<span class="day-num">${ d.day }</span>
								<a
									href="javascript:void(0);"
									v-for="b in booking[`${d.year}-${d.month}-${this.padZero(d.day, 2)}`]"
									@click="showBooking(b)"
									class="even"
								>
									${ formatTime(b.start_at) }
									<br />
									${ b.title }
									<br />
									<span v-html="showStatus(b)"></span>
								</a>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
{{ end }}

{{define "member.assets"}}
	<link rel="stylesheet" href="/assets/css/member.min.css?v=0115" />
	<link rel="stylesheet" href="/assets/css/member_rwd.min.css?v=0115" />
	<script src="/assets/js/member.min.js?v=0115"></script>
{{ end }}
